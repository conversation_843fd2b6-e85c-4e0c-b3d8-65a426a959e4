from utils.permissions import <PERSON><PERSON><PERSON><PERSON><PERSON>, IsAdmin

from django.shortcuts import get_object_or_404
from django.conf import settings

from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import api_view

from .models import *
from .serializers import *


class BlogCategoryView(APIView):
    permission_classes = [IsSupervisor]

    def get(self, request):
        queryset = BlogCategory.objects.all()
        serializer = BlogCategorySerializer(queryset, many=True)
        return Response(serializer.data)

    def post(self, request):
        serializer = BlogCategorySerializer(
            data=request.data, context={'request': request}, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class BlogPostView(APIView):
    permission_classes = [IsSupervisor]

    def get(self, request, pk=None):
        if pk:
            blog = BlogPost.objects.get(id=pk)
            serializer = BlogPostSerializer(
                blog, context={'request': request})
        else:
            blog = BlogPost.objects.all().order_by('-created_at')
            serializer = BlogPostSerializer(
                blog, many=True, context={'request': request})
        return Response(serializer.data)

    def post(self, request):
        serializer = BlogPostSerializer(
            data=request.data, context={'request': request}, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def patch(self, request, pk=None):
        blog = BlogPost.objects.get(id=pk)
        serializer = BlogPostSerializer(
            blog, data=request.data, context={'request': request}, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk=None):
        blog = get_object_or_404(BlogPost, id=pk)
        if blog:
            blog.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class BlogCommentView(APIView):

    def get(self, request):
        queryset = BlogComment.objects.all().order_by('-created_at')
        serializer = BlogCommentSerializer(queryset, many=True)
        return Response(serializer.data)

    def post(self, request):
        serializer = BlogCommentSerializer(
            data=request.data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class BlogTagView(APIView):

    def get(self, request):
        queryset = BlogTag.objects.all()
        serializer = BlogTagSerializer(queryset, many=True)
        return Response(serializer.data)


@api_view(['POST'])
def create_blog_post(request):
    api_key = request.GET.get('api_key')

    # Check API key
    if api_key != settings.REMOTE_API_KEY:
        return Response(status=status.HTTP_404_NOT_FOUND)

    default_user = User.objects.get(username='<EMAIL>')
    request.user = default_user

    serializer = BlogPostSerializer(
        data=request.data, context={'request': request}, partial=True)
    if serializer.is_valid():
        serializer.save()
        return Response({"message": "success", "blog_id": serializer.data["id"]}, status=status.HTTP_201_CREATED)
    return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)
