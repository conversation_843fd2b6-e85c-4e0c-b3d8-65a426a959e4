from django.shortcuts import render
from .models import *
from .serializers import *

from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from utils.permissions import IsSupervisor, IsAdmin


# Create your views here.
class StudentView(APIView):
    permission_classes = [IsAdmin]

    def get(self, request, pk=""):
        if pk == "":
            students = Student.objects.all().order_by('-id')
            serializer = StudentSerializer(
                students, context={'request': request}, many=True)
        else:
            student = Student.objects.get(id=pk)
            serializer = StudentSerializer(
                student, context={'request': request})
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request):
        serializer = StudentSerializer(
            data=request.data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def patch(self, request, pk):
        student = Student.objects.get(id=pk)
        serializer = StudentSerializer(
            student, data=request.data, context={'request': request}, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        student = Student.objects.get(id=pk)
        student.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class TeacherView(APIView):
    permission_classes = [IsAdmin]

    def get(self, request, pk=""):
        if pk == "":
            teachers = Teacher.objects.all().order_by('-id')
            serializer = TeacherSerializer(
                teachers, context={'request': request}, many=True)
        else:
            teacher = Teacher.objects.get(id=pk)
            serializer = TeacherSerializer(
                teacher, context={'request': request})
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request):
        serializer = TeacherSerializer(
            data=request.data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def patch(self, request, pk):
        teacher = Teacher.objects.get(id=pk)
        serializer = TeacherSerializer(
            teacher, data=request.data, context={'request': request}, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        teacher = Teacher.objects.get(id=pk)
        teacher.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class ClassView(APIView):
    permission_classes = [IsAdmin]

    def get(self, request, pk=""):
        if pk == "":
            classes = Class.objects.all().order_by('-start_date')
            serializer = ClassSerializer(
                classes, context={'request': request}, many=True)
        else:
            class_obj = Class.objects.get(id=pk)
            serializer = ClassSerializer(
                class_obj, context={'request': request})
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request):
        serializer = ClassSerializer(
            data=request.data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def patch(self, request, pk):
        class_obj = Class.objects.get(id=pk)
        serializer = ClassSerializer(
            class_obj, data=request.data, context={'request': request}, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        class_obj = Class.objects.get(id=pk)
        class_obj.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class ClassStudentView(APIView):
    permission_classes = [IsAdmin]

    def get(self, request, class_pk=None):
        """
        GET /api/academy/class-student/ - Lấy tất cả quan hệ lớp-học viên
        GET /api/academy/class-student/<class_pk> - Lấy tất cả học viên trong lớp <class_pk>
        """
        if class_pk is None:
            # Lấy tất cả quan hệ lớp-học viên
            class_students = ClassStudent.objects.all()
            serializer = ClassStudentSerializer(
                class_students, context={'request': request}, many=True)
        else:
            # Lấy tất cả học viên trong lớp <class_pk>
            try:
                class_obj = Class.objects.get(id=class_pk)
                class_students = ClassStudent.objects.filter(
                    classroom=class_obj).order_by('-id')
                serializer = ClassStudentSerializer(
                    class_students, context={'request': request}, many=True)
            except Class.DoesNotExist:
                return Response(
                    {"detail": "Không tìm thấy lớp học."},
                    status=status.HTTP_404_NOT_FOUND
                )
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request):
        """
        POST /api/academy/class-student/ - Tạo mới quan hệ lớp-học viên
        """
        serializer = ClassStudentSerializer(
            data=request.data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class LessonView(APIView):
    permission_classes = [IsAdmin]

    def get(self, request, pk=""):
        if pk == "":
            lessons = Lesson.objects.all().order_by('class_ref', 'lesson_no')
            serializer = LessonSerializer(
                lessons, context={'request': request}, many=True)
        else:
            lesson = Lesson.objects.get(id=pk)
            serializer = LessonSerializer(
                lesson, context={'request': request})
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request):
        serializer = LessonSerializer(
            data=request.data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def patch(self, request, pk):
        lesson = Lesson.objects.get(id=pk)
        serializer = LessonSerializer(
            lesson, data=request.data, context={'request': request}, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        lesson = Lesson.objects.get(id=pk)
        lesson.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class ClassStudentDetailView(APIView):
    permission_classes = [IsAdmin]

    def delete(self, request, class_pk, student_pk):
        """
        DELETE /api/academy/class-student/<class_pk>/student/<student_pk> - Xóa học viên khỏi lớp
        """
        try:
            # Xóa học viên khỏi lớp
            class_student = ClassStudent.objects.get(
                classroom_id=class_pk, student_id=student_pk)
            class_student.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except ClassStudent.DoesNotExist:
            return Response(
                {"detail": "Không tìm thấy học viên trong lớp này."},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    def patch(self, request, class_pk, student_pk):
        """
        PATCH /api/academy/class-student/<class_pk>/student/<student_pk> - Cập nhật thông tin học viên trong lớp
        """
        try:
            # Cập nhật thông tin học viên trong lớp
            class_student = ClassStudent.objects.get(
                classroom_id=class_pk, student_id=student_pk)

            # Cập nhật các trường từ request
            if 'fee' in request.data:
                class_student.fee = request.data['fee']
            if 'discount' in request.data:
                class_student.discount = request.data['discount']
            if 'paid' in request.data:
                class_student.paid = request.data['paid']
            if 'note' in request.data:
                class_student.note = request.data['note']
            if 'certificate_no' in request.data:
                class_student.certificate_no = request.data['certificate_no']

            class_student.save()

            # Trả về thông tin đã cập nhật
            serializer = ClassStudentSerializer(class_student)
            return Response(serializer.data)
        except ClassStudent.DoesNotExist:
            return Response(
                {"detail": "Không tìm thấy học viên trong lớp này."},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)
