from rest_framework import serializers
from .models import *


class StudentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Student
        fields = "__all__"


class TeacherSerializer(serializers.ModelSerializer):
    class Meta:
        model = Teacher
        fields = "__all__"


class ClassSerializer(serializers.ModelSerializer):
    teacher = TeacherSerializer(read_only=True)
    teacher_id = serializers.PrimaryKeyRelatedField(
        source='teacher',
        queryset=Teacher.objects.all(),
        write_only=True,
        required=False,
        allow_null=True
    )

    class Meta:
        model = Class
        fields = ['id', 'name', 'start_date', 'number_of_lessons', 'fee', 'discount',
                  'schedule', 'room', 'teacher', 'teacher_id', 'completed', 'status']

    def create(self, validated_data):
        return Class.objects.create(**validated_data)

    def update(self, instance, validated_data):
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance


class ClassStudentSerializer(serializers.ModelSerializer):
    student = StudentSerializer(read_only=True)
    classroom = ClassSerializer(read_only=True)
    student_id = serializers.PrimaryKeyRelatedField(
        source='student',
        queryset=Student.objects.all(),
        write_only=True
    )
    classroom_id = serializers.PrimaryKeyRelatedField(
        source='classroom',
        queryset=Class.objects.all(),
        write_only=True
    )

    class Meta:
        model = ClassStudent
        fields = ['id', 'student', 'classroom', 'joined_date', 'student_id',
                  'classroom_id', 'fee', 'discount', 'paid', 'note', 'certificate_no']


class AttendanceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Attendance
        fields = "__all__"


class InvoiceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Invoice
        fields = "__all__"


class LessonSerializer(serializers.ModelSerializer):
    class_ref = ClassSerializer(read_only=True)
    class_ref_id = serializers.PrimaryKeyRelatedField(
        queryset=Class.objects.all(), source='class_ref', write_only=True)

    class Meta:
        model = Lesson
        fields = ['id', 'class_ref', 'lesson_no', 'class_ref_id',
                  'title', 'date', 'content', 'result_summary']
