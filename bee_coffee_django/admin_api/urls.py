from django.urls import path
from .views import *

urlpatterns = [
    path("cart/<str:pk>", CartView.as_view(), name='Cart'),
    path("user/", UserView.as_view(), name="User"),
    path("user/<str:pk>", UserView.as_view(), name="User"),
    path("promotion/", PromotionView.as_view(), name="Promotion"),
    path("promotion/<str:pk>", PromotionView.as_view(), name="Promotion"),
    path("coupon/", CouponView.as_view(), name="Coupon"),
    path("coupon/<str:pk>", CouponView.as_view(), name="Coupon"),
    path("single-page/<slug:pk>", SinglePageView.as_view(), name="SingePage"),
    path("public-single-page/<slug:pk>",
         PublicSinglePageView.as_view(), name="PublicSinglePage"),
    path("carousel/", CarouselView.as_view(), name="Carousel"),
    path("public-carousel/", PublicCarouselView.as_view(), name="PublicCarousel"),

    # Dashboard API
    path("dashboard/top-product", top_product, name="TopProduct"),
    path("dashboard/percentage-category",
         percentage_category, name="PercentageCategory"),
    path("dashboard/total-invoice", total_invoice, name="TotalInvoice"),
    path("dashboard/total-revenue", total_revenue, name="TotalRevenue"),
    path("dashboard/total-user", total_user, name="TotalUser"),
    path("dashboard/total-net-revenue",
         total_net_revenue, name="TotalNetRevenue"),
    path("dashboard/cost-of-goods-sold",
         cost_of_goods_sold, name="CostOfGoodsSold"),
    path("dashboard/total-gross-profit",
         total_gross_profit, name="TotalGrossProfit"),
    path("dashboard/revenue-duration", revenue_duration, name="Revenue"),
    path("transaction", TransactionView.as_view(), name="Transaction"),
    path("transaction/<str:pk>", TransactionView.as_view(), name="Transaction"),
    path("transaction-tags", TransactionTagView.as_view(), name="TransactionTags"),
    path("upload-image", upload_image, name="UploadImage"),
]
