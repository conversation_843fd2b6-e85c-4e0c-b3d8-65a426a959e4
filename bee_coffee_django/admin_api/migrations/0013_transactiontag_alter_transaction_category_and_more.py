# Generated by Django 4.2.6 on 2025-08-06 22:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('admin_api', '0012_alter_transaction_date'),
    ]

    operations = [
        migrations.CreateModel(
            name='TransactionTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('slug', models.SlugField(blank=True, max_length=100, unique=True)),
            ],
        ),
        migrations.AlterField(
            model_name='transaction',
            name='category',
            field=models.CharField(choices=[('cast', 'Tiền mặt'), ('banking', 'Chuyển khoản'), ('visa', 'Visa')], default='cast', max_length=100),
        ),
        migrations.AddField(
            model_name='transaction',
            name='tags',
            field=models.ManyToManyField(blank=True, to='admin_api.transactiontag'),
        ),
    ]
