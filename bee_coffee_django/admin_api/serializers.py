from rest_framework import serializers
from .models import *

from product_api.serializers import (
    ProductSerializer
)


class PromotionSerializer(serializers.ModelSerializer):
    products = serializers.ListField(
        child=serializers.CharField(), write_only=True)
    products_data = ProductSerializer(
        source='products', many=True, read_only=True)
    coupon_data = serializers.SerializerMethodField()

    class Meta:
        model = Promotion
        fields = ("id", "title", "description", "promotion_type",
                  "discount_value", "start_date", "end_date", "is_valid",
                  "min_purchase_amount", "products", "products_data", "coupon_data")

    def get_coupon_data(self, obj):
        coupons = Coupon.objects.filter(promotion=obj)
        return CouponSerializer(coupons, many=True).data

    def update(self, instance, validated_data):
        # Extract products SKUs from validated_data
        products_skus = validated_data.pop('products', [])
        # Update other fields
        instance = super().update(instance, validated_data)

        # Update the products field
        if products_skus:
            products = Inventory.objects.filter(sku__in=products_skus)
            # Set many-to-many relation with Inventory objects
            instance.products.set(products)
        return instance


class CouponSerializer(serializers.ModelSerializer):

    promotion_title = serializers.SerializerMethodField()

    class Meta:
        model = Coupon
        fields = ("code", "promotion", "promotion_title", "usage_limit",
                  "used_count", "valid_from", "valid_to", "is_valid")

    def get_promotion_title(self, obj):
        return obj.promotion.title


class SinglePageSerializer(serializers.ModelSerializer):
    class Meta:
        model = SinglePage
        fields = '__all__'


class CarouselSerializer(serializers.ModelSerializer):
    image_url = serializers.SerializerMethodField()

    class Meta:
        model = Carousel
        fields = ["title", "image", "image_url"]

    def get_image_url(self, obj):
        request = self.context.get('request')
        if obj.image:
            return request.build_absolute_uri(obj.image.url)
        return None


class TransactionTagSerializer(serializers.ModelSerializer):
    class Meta:
        model = TransactionTag
        fields = ["id", "name", "slug"]


class TransactionSerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()
    tags = TransactionTagSerializer(many=True, read_only=True)
    tags_data = serializers.ListField(
        child=serializers.CharField(), write_only=True, required=False)

    class Meta:
        model = Transaction
        fields = ["id", "date", "type", "category", "amount",
                  "description", "name", "tags", "tags_data"]

    def get_name(self, obj):
        return obj.user.profile.name()

    def create(self, validated_data):
        """Create a new transaction with category and tags"""
        tags_data = validated_data.pop('tags_data', [])

        transaction = Transaction.objects.create(
            **validated_data,
            user=self.context['request'].user)

        # Handle tags
        if tags_data:
            for tag_name in tags_data:
                tag, created = TransactionTag.objects.get_or_create(
                    name=tag_name)
                transaction.tags.add(tag)

        return transaction

    def update(self, instance, validated_data):
        """Update transaction with category and tags"""
        tags_data = validated_data.pop('tags_data', None)

        # Update other fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Handle tags
        if tags_data is not None:
            instance.tags.clear()
            for tag_name in tags_data:
                tag, created = TransactionTag.objects.get_or_create(
                    name=tag_name)
                instance.tags.add(tag)

        return instance
