from datetime import datetime
from django.db import models
from django.utils import timezone
from django.utils.text import slugify

from django.contrib.auth.models import User

from product_api.models import (
    Inventory
)


# Create your models here.
class ProvinceVietNam(models.Model):
    province = models.TextField(max_length=200)
    ward = models.TextField(max_length=500)
    timeUpdate = models.DateTimeField(auto_now_add=True, editable=False)

    def __str__(self):
        return f"{self.province} - {self.ward}"


class Promotion(models.Model):
    PROMOTION_TYPE_CHOICES = [
        ('PERCENTAGE', 'Percentage'),
        ('FIXED_AMOUNT', 'Fixed Amount'),
        ('BUY_ONE_GET_ONE', 'Buy One Get One Free'),
    ]

    title = models.CharField(max_length=255, help_text="Name of the promotion")
    description = models.TextField(null=True, blank=True)
    promotion_type = models.CharField(
        max_length=20, choices=PROMOTION_TYPE_CHOICES)
    discount_value = models.DecimalField(max_digits=10, decimal_places=2, null=True,
                                         blank=True, help_text="Percentage or fixed amount value for the discount")
    start_date = models.DateTimeField(default=timezone.now)
    end_date = models.DateTimeField(null=True, blank=True)

    min_purchase_amount = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True, help_text="Minimum purchase amount for the promotion to apply")
    # Many products can be part of a promotion
    products = models.ManyToManyField(
        Inventory, related_name="promotions", blank=True)
    create_time = models.DateTimeField(auto_now_add=True, editable=False)

    def __str__(self):
        return self.title

    def is_valid(self):
        return self.start_date <= datetime.now() <= self.end_date


class Coupon(models.Model):
    code = models.CharField(max_length=20, unique=True)
    promotion = models.ForeignKey(Promotion, on_delete=models.CASCADE)
    usage_limit = models.IntegerField(
        default=1, help_text="Maximum number of times the coupon can be used")
    used_count = models.IntegerField(default=0)
    valid_from = models.DateTimeField(default=timezone.now)
    valid_to = models.DateTimeField(null=True, blank=True)

    def is_valid(self):
        return self.used_count < self.usage_limit and datetime.now() >= self.valid_from and (self.valid_to is None or datetime.now() <= self.valid_to)

    def __str__(self):
        return f"{self.code} - {self.promotion.title}"


class SinglePage(models.Model):
    title = models.CharField(max_length=255)
    slug = models.SlugField(unique=True)
    content = models.TextField()
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title


class Carousel(models.Model):
    title = models.CharField(max_length=255, unique=True)
    image = models.ImageField(upload_to="carousel")
    link = models.URLField()
    order = models.IntegerField(default=0)

    def __str__(self):
        return self.title


class Transaction(models.Model):
    """Quản lý thu chi"""
    TRANSACTION_TYPES = [
        ('income', 'Thu'),
        ('expense', 'Chi'),
    ]
    CATEGORY_TYPE = [
        ('cast', 'Tiền mặt'),
        ('banking', 'Chuyển khoản'),
        ('visa', 'Visa'),
    ]
    user = models.ForeignKey(User, on_delete=models.CASCADE)  # Người thực hiện
    type = models.CharField(max_length=10, choices=TRANSACTION_TYPES)
    # Ví dụ: "Lương", "Nhập hàng", "Quảng cáo"
    category = models.CharField(
        max_length=100, choices=CATEGORY_TYPE, default='cast')
    tags = models.ManyToManyField('TransactionTag', blank=True)
    amount = models.IntegerField(default=0)
    description = models.TextField(blank=True, null=True)
    date = models.DateField(default=None, null=True)  # Ngày giao dịch
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.get_type_display()} - {self.amount} ({self.category})"


class TransactionTag(models.Model):
    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(max_length=100, unique=True, blank=True)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name
