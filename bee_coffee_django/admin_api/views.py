import os
from PIL import Image
import io

from utils.Utils import parse_date
from utils.permissions import <PERSON><PERSON>d<PERSON>, IsSupervisor

from django.shortcuts import get_object_or_404
from django.contrib.auth.models import User

from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.parsers import MultiPartParser, FormParser
from rest_framework.permissions import IsAuthenticated

# Create your views here.
from .models import *
from .serializers import *

from auth_api.models import (
    Profile
)

from shopping_api.models import (
    Cart,
    CartDetail
)

from auth_api.serializers import (
    ProfileSerializer
)

from shopping_api.serializers import (
    CartSerializer,
    CartDetailSerializer
)


"""Supervisor Permission API"""


class UserView(APIView):
    permission_classes = [IsSupervisor]

    def get(self, request, pk=""):
        if pk == "":
            profile = Profile.objects.all()
            serializer = ProfileSerializer(
                profile, context={'request': request}, many=True)
        else:
            user = User.objects.get(username=pk)
            profile = Profile.objects.get(user=user)
            serializer = ProfileSerializer(
                profile, context={'request': request})
        return Response(serializer.data, status=status.HTTP_200_OK)

    def patch(self, request, pk=""):
        if pk == "":
            return Response({"error": "Không tìm thấy user!"}, status=status.HTTP_404_NOT_FOUND)
        user = User.objects.get(username=pk)
        profile = Profile.objects.get(user=user)
        serializer = ProfileSerializer(
            profile, data=request.data, context={'request': request}, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CartView(APIView):
    permission_classes = [IsSupervisor]

    def patch(self, request, pk=""):
        try:
            cart = Cart.objects.get(id=pk)
        except Cart.DoesNotExist:
            return Response({"message": "Không tìm thấy thông tin đơn hàng"}, status=status.HTTP_404_NOT_FOUND)
        serializer = CartSerializer(cart, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            if "payment_method" in request.data:
                cartDetail = CartDetail.objects.filter(cart=cart)
                for _item in cartDetail:
                    _item.item.quantity -= _item.quantity
                    _item.item.save()
            return Response(status=status.HTTP_200_OK)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class PromotionView(APIView):
    permission_classes = [IsSupervisor]

    def get(self, request, pk=""):
        if pk == "":
            promotion = Promotion.objects.all()
            serializer = PromotionSerializer(
                promotion, context={'request': request}, many=True)
        else:
            try:
                promotion = Promotion.objects.get(id=pk)
                serializer = PromotionSerializer(
                    promotion, context={'request': request})
            except Promotion.DoesNotExist:
                return Response(status=status.HTTP_404_NOT_FOUND)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request):
        serializer = PromotionSerializer(
            data=request.data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def patch(self, request):
        promotion_id = request.data.get('id')
        promotion = get_object_or_404(Promotion, id=promotion_id)
        serializer = PromotionSerializer(
            promotion, data=request.data, partial=True, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        promotion = get_object_or_404(Promotion, id=pk)
        promotion.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class CouponView(APIView):
    permission_classes = [IsSupervisor]

    def get(self, request, pk=""):
        if pk == "":
            coupon = Coupon.objects.all()
            serializer = CouponSerializer(
                coupon, context={'request': request}, many=True)
        else:
            try:
                coupon = Coupon.objects.get(code=pk)
                serializer = CouponSerializer(
                    coupon, context={'request': request})
            except Coupon.DoesNotExist:
                return Response(status=status.HTTP_404_NOT_FOUND)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request):
        serializer = CouponSerializer(
            data=request.data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def patch(self, request):
        code = request.data.get('code')
        coupon = get_object_or_404(Coupon, code=code)
        serializer = CouponSerializer(
            coupon, data=request.data, partial=True, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        coupon = get_object_or_404(Coupon, code=pk)
        coupon.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class SinglePageView(APIView):
    permission_classes = [IsSupervisor]

    def get(self, request, pk=""):
        if pk:
            page = get_object_or_404(SinglePage, slug=pk)
            serializer = SinglePageSerializer(page)
        else:
            return Response(status=status.HTTP_404_NOT_FOUND)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request):
        serializer = SinglePageSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def put(self, request, pk):
        page = get_object_or_404(SinglePage, slug=pk)
        serializer = SinglePageSerializer(page, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CarouselView(APIView):
    permission_classes = [IsSupervisor]
    parser_classes = [MultiPartParser, FormParser]

    def get(self, request):
        carousel = Carousel.objects.all()
        serializer = CarouselSerializer(
            carousel, context={'request': request}, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request):
        if request.FILES.getlist('images') or request.data.getlist('removedImages[]', []):
            for image in request.FILES.getlist('images'):
                carousel_obj, _ = Carousel.objects.get_or_create(
                    title=image.name,
                )
                carousel_obj.image = image
                carousel_obj.save()
            for image in request.data.getlist('removedImages[]', []):
                carousel_obj = get_object_or_404(Carousel, title=image)
                carousel_obj.delete()
        return Response(status=status.HTTP_200_OK)


# Dashboard API
@api_view(['GET'])
@permission_classes([IsSupervisor])
def top_product(request):
    dateFrom = request.GET.get('dateFrom', None)
    dateTo = request.GET.get('dateTo', None)

    dateFrom = parse_date(dateFrom)
    dateTo = parse_date(dateTo)

    cart = Cart.objects.filter(paid=True, orderTime__range=[
                               dateFrom, dateTo])

    cartDetail = CartDetail.objects.filter(cart__in=cart)
    data = {}
    for item in cartDetail:
        if item.item.sku in data:
            data[item.item.sku]['quantity'] += item.quantity
            data[item.item.sku]['price'] += item.quantity * \
                item.apply_promotion()
        else:
            data[item.item.sku] = {
                'item': ProductSerializer(item.item, context={'request': request}).data,
                'quantity': item.quantity,
                'price': item.quantity * item.apply_promotion()
            }
    data = {k: v for k, v in sorted(
        data.items(), key=lambda item: item[1]['price'], reverse=True)}
    list_data = []
    for index, item in enumerate(data):
        list_data.append({
            'sku': item,
            'item': data[item]['item'],
            'quantity': data[item]['quantity'],
            'price': data[item]['price']
        })
        if index == 10:
            break
    return Response(list_data, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([IsSupervisor])
def percentage_category(request):
    dateFrom = request.GET.get('dateFrom', None)
    dateTo = request.GET.get('dateTo', None)

    dateFrom = parse_date(dateFrom)
    dateTo = parse_date(dateTo)

    cart = Cart.objects.filter(paid=True, orderTime__range=[
                               dateFrom, dateTo])

    cartDetail = CartDetail.objects.filter(cart__in=cart)
    data = {}
    for item in cartDetail:
        if item.item.category.name in data:
            data[item.item.category.name]['quantity'] += item.quantity
            data[item.item.category.name]['price'] += item.quantity * \
                item.apply_promotion()
        else:
            data[item.item.category.name] = {
                'category': item.item.category.name,
                'quantity': item.quantity,
                'price': item.quantity * item.apply_promotion()
            }
    total_price = sum([data[item]['price'] for item in data])
    for item in data:
        data[item]['percentage'] = data[item]['price'] / total_price * 100
    list_data = []
    for index, category in enumerate(data):
        list_data.append({
            'id': index + 1,
            'value': data[category]['percentage'],
            'label': data[category]['category'],
        })
    return Response(list_data, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([IsSupervisor])
def total_invoice(request):
    dateFrom = request.GET.get('dateFrom', None)
    dateTo = request.GET.get('dateTo', None)

    dateFrom = parse_date(dateFrom)
    dateTo = parse_date(dateTo)

    cart = Cart.objects.filter(paid=True, orderTime__range=[
        dateFrom, dateTo])

    return Response({"total": len(cart)}, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([IsSupervisor])
def total_revenue(request):
    dateFrom = request.GET.get('dateFrom', None)
    dateTo = request.GET.get('dateTo', None)

    dateFrom = parse_date(dateFrom)
    dateTo = parse_date(dateTo)

    cart = Cart.objects.filter(paid=True, orderTime__range=[
        dateFrom, dateTo])

    cartDetail = CartDetail.objects.filter(cart__in=cart)
    total = 0
    for item in cartDetail:
        total += item.quantity * item.apply_promotion()
    return Response({"total": total}, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([IsSupervisor])
def total_user(request):
    dateFrom = request.GET.get('dateFrom', None)
    dateTo = request.GET.get('dateTo', None)

    dateFrom = parse_date(dateFrom)
    dateTo = parse_date(dateTo)

    user = Profile.objects.filter(date_joined__range=[dateFrom, dateTo])

    return Response({"total": len(user)}, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([IsSupervisor])
def total_net_revenue(request):
    dateFrom = request.GET.get('dateFrom', None)
    dateTo = request.GET.get('dateTo', None)

    dateFrom = parse_date(dateFrom)
    dateTo = parse_date(dateTo)

    total = calculate_net_revenue(dateFrom, dateTo)

    return Response({"total": total}, status=status.HTTP_200_OK)


def calculate_net_revenue(dateFrom, dateTo):
    cart = Cart.objects.filter(paid=True, orderTime__range=[
        dateFrom, dateTo])
    cartDetail = CartDetail.objects.filter(cart__in=cart)
    total = 0
    for item in cartDetail:
        total += item.quantity * \
            (item.apply_promotion() - item.item.regular_price)
    return total


@api_view(['GET'])
@permission_classes([IsSupervisor])
def cost_of_goods_sold(request):
    dateFrom = request.GET.get('dateFrom', None)
    dateTo = request.GET.get('dateTo', None)

    dateFrom = parse_date(dateFrom)
    dateTo = parse_date(dateTo)

    total = calculate_cogs()
    return Response({"total": total}, status=status.HTTP_200_OK)


def calculate_cogs():
    inventory = Inventory.objects.all()
    total = 0
    for item in inventory:
        total += item.quantity * item.regular_price
    return total


@api_view(['GET'])
@permission_classes([IsSupervisor])
def total_gross_profit(request):
    dateFrom = request.GET.get('dateFrom', None)
    dateTo = request.GET.get('dateTo', None)

    dateFrom = parse_date(dateFrom)
    dateTo = parse_date(dateTo)

    total = float(calculate_net_revenue(dateFrom, dateTo)) - \
        float(calculate_cogs())
    return Response({"total": total}, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([IsSupervisor])
def revenue_duration(request):
    dateFrom = request.GET.get('dateFrom', None)
    dateTo = request.GET.get('dateTo', None)

    dateFrom = parse_date(dateFrom)
    dateTo = parse_date(dateTo)

    cart = Cart.objects.filter(paid=True, orderTime__range=[
                               dateFrom, dateTo])
    cartDetail = CartDetail.objects.filter(cart__in=cart)
    data = {}
    for item in cartDetail:
        if item.cart.orderTime.strftime("%d/%m/%Y") in data:
            data[item.cart.orderTime.strftime(
                "%d/%m/%Y")]['revenue'] += item.quantity * item.apply_promotion()
            data[item.cart.orderTime.strftime("%d/%m/%Y")]['profit'] += item.quantity * (
                item.apply_promotion() - item.item.regular_price)
        else:
            data[item.cart.orderTime.strftime("%d/%m/%Y")] = {
                'revenue': item.quantity * item.apply_promotion(),
                'profit': item.quantity * (item.apply_promotion() - item.item.regular_price)
            }
    list_data = []
    for item in data:
        list_data.append({
            'date': item,
            'revenue': round(data[item]['revenue']/1000000, 2),
            'profit': round(data[item]['profit']/1000000, 2)
        })
    return Response(list_data, status=status.HTTP_200_OK)


class TransactionView(APIView):
    permission_classes = [IsSupervisor]

    def get(self, request, pk=None):
        if not pk:
            dateFrom = request.GET.get('dateFrom', None)
            dateTo = request.GET.get('dateTo', None)

            dateFrom = parse_date(dateFrom)
            dateTo = parse_date(dateTo)

            transactions = Transaction.objects.filter(
                date__range=[dateFrom, dateTo]).order_by('-date')
            serializers = TransactionSerializer(
                transactions, many=True, context={'request': request})
            return Response(serializers.data, status=status.HTTP_200_OK)
        else:
            try:
                transaction = Transaction.objects.get(id=pk)
                serializer = TransactionSerializer(
                    transaction, context={'request': request})
                return Response(serializer.data, status=status.HTTP_200_OK)
            except Transaction.DoesNotExist:
                return Response({"error", "Phiếu không tồn tại"}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request):
        serializer = TransactionSerializer(
            data=request.data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def put(self, request, pk=None):
        if pk:
            try:
                transaction = Transaction.objects.get(id=pk)
                serializer = TransactionSerializer(
                    transaction, data=request.data, context={'request': request})
                if serializer.is_valid():
                    serializer.save()
                    return Response(serializer.data, status=status.HTTP_200_OK)
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            except Transaction.DoesNotExist:
                return Response({"error": "Phiếu không tồn tại"}, status=status.HTTP_404_NOT_FOUND)
        return Response({"error": "Không có mã phiếu"}, status=status.HTTP_404_NOT_FOUND)

    def delete(self, request, pk=None):
        if pk:
            try:
                transaction = Transaction.objects.get(id=pk)
                transaction.delete()
                return Response(status=status.HTTP_200_OK)
            except Transaction.DoesNotExist:
                return Response({"error": "Phiếu không tồn tại"}, status=status.HTTP_404_NOT_FOUND)
        return Response({"error": "Không có mã phiếu"}, status=status.HTTP_404_NOT_FOUND)


"""Public API"""


class PublicSinglePageView(APIView):

    def get(self, request, pk=""):
        if pk:
            page = get_object_or_404(SinglePage, slug=pk)
            serializer = SinglePageSerializer(page)
        else:
            return Response(status=status.HTTP_404_NOT_FOUND)
        return Response(serializer.data, status=status.HTTP_200_OK)


class PublicCarouselView(APIView):

    def get(self, request):
        carousel = Carousel.objects.all()
        serializer = CarouselSerializer(
            carousel, context={'request': request}, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


@api_view(['POST'])
@permission_classes([IsSupervisor])
@parser_classes([MultiPartParser, FormParser])
def upload_image(request):
    if 'upload' not in request.FILES:
        return Response({'error': 'No image file provided'}, status=status.HTTP_400_BAD_REQUEST)

    image_file = request.FILES['upload']

    # Kiểm tra định dạng file
    allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    if image_file.content_type not in allowed_types:
        return Response({'error': 'Invalid file type. Allowed types: JPG, PNG, GIF, WEBP'},
                        status=status.HTTP_400_BAD_REQUEST)

    # Kiểm tra kích thước file (giới hạn 5MB)
    if image_file.size > 5 * 1024 * 1024:
        return Response({'error': 'File size too large. Maximum size is 5MB'},
                        status=status.HTTP_400_BAD_REQUEST)

    try:
        # Mở ảnh với Pillow
        img = Image.open(image_file)

        # Kiểm tra và resize nếu width > 1080
        max_width = 1920
        if img.width > max_width:
            # Tính tỷ lệ để giữ aspect ratio
            ratio = max_width / img.width
            new_height = int(img.height * ratio)

            # Resize ảnh
            img = img.resize((max_width, new_height), Image.Resampling.LANCZOS)

            # Chuyển ảnh đã resize về bytes
            img_byte_arr = io.BytesIO()

            # Lưu với định dạng gốc
            format_name = image_file.content_type.split('/')[-1].upper()
            if format_name == 'JPG':
                format_name = 'JPEG'

            # Lưu với chất lượng cao nếu là JPEG
            if format_name == 'JPEG':
                img.save(img_byte_arr, format=format_name, quality=95)
            else:
                img.save(img_byte_arr, format=format_name)

            img_byte_arr.seek(0)
            image_file = img_byte_arr

        # Tạo tên file ngẫu nhiên để tránh trùng lặp
        import uuid
        file_extension = os.path.splitext(image_file.name)[1] if hasattr(
            image_file, 'name') else f".{format_name.lower()}"
        new_filename = f"{uuid.uuid4()}{file_extension}"

        # Tạo đường dẫn lưu file theo năm/tháng
        from datetime import datetime
        upload_path = datetime.now().strftime('uploads/%Y/%m/')
        full_path = os.path.join('media', upload_path)

        # Tạo thư mục nếu chưa tồn tại
        os.makedirs(full_path, exist_ok=True)

        # Lưu file
        file_path = os.path.join(full_path, new_filename)
        with open(file_path, 'wb+') as destination:
            if isinstance(image_file, io.BytesIO):
                destination.write(image_file.getvalue())
            else:
                for chunk in image_file.chunks():
                    destination.write(chunk)

        # Trả về URL đầy đủ
        image_url = f"media/{upload_path}{new_filename}"

        return Response({
            'url': image_url,
            'uploaded': 1,
            'fileName': new_filename
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TransactionTagView(APIView):
    permission_classes = [IsSupervisor]

    def get(self, request):
        """Get all transaction tags"""
        tags = TransactionTag.objects.all().order_by('name')
        serializer = TransactionTagSerializer(tags, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
