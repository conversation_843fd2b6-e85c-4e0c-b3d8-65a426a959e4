import React from "react";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TablePagination from "@mui/material/TablePagination";
import TableRow from "@mui/material/TableRow";
import AdminLayout from "../Common/AdminLayout2";
import { Box, Button, IconButton, Modal, Typography, FormControl, InputLabel, Select, MenuItem, Autocomplete, Chip, Card, CardContent } from "@mui/material";
import Grid from '@mui/material/Grid2';
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
import CustomDatePicker from "../../Common/CustomDatePicker";
import CustomTextField from "../../Common/CustomTextField";
import axiosInstance from "../../../services/axiosInstance";
import Loading from "../../Common/Loading";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import "react-date-range/dist/styles.css"; // main css file
import "react-date-range/dist/theme/default.css"; // theme css file
import { Calendar } from "react-date-range";
import CheckIcon from "@mui/icons-material/Check";
import { useDocumentTitle } from "../../../hooks/useDocumentTitle";
import { green, red } from "@mui/material/colors";
import { BarChart } from '@mui/x-charts/BarChart';
import { PieChart } from '@mui/x-charts/PieChart';

function TransactionCharts({ transactions }) {
    // Palette màu cho charts
    const colorPalette = [
        '#2E7D32', '#388E3C', '#43A047', '#4CAF50', '#66BB6A', '#81C784', '#A5D6A7',
        '#C8E6C9', '#E8F5E8', '#1976D2', '#1E88E5', '#2196F3', '#42A5F5', '#64B5F6',
        '#90CAF9', '#BBDEFB', '#E3F2FD', '#F57C00', '#FF9800', '#FFB74D', '#FFCC02'
    ];

    // Tính toán dữ liệu cho biểu đồ
    const incomeTransactions = transactions.filter(t => t.type === 'income');
    const expenseTransactions = transactions.filter(t => t.type === 'expense');

    const totalIncome = incomeTransactions.reduce((sum, t) => sum + t.amount, 0);
    const totalExpense = expenseTransactions.reduce((sum, t) => sum + t.amount, 0);
    const profit = totalIncome - totalExpense;

    // Dữ liệu cho Bar Chart Thu-Chi
    const barChartData = [
        { label: 'Thu', value: totalIncome, color: green[600] },
        { label: 'Chi', value: totalExpense, color: red[600] },
    ];

    // Dữ liệu cho Pie Chart % Thu-Chi
    const incomeExpensePieData = [
        { id: 0, value: totalIncome, label: 'Thu', color: green[600] },
        { id: 1, value: totalExpense, label: 'Chi', color: red[600] },
    ];

    // Tính toán % thu từ các tag
    const incomeTagStats = {};
    incomeTransactions.forEach(transaction => {
        if (transaction.tags && transaction.tags.length > 0) {
            transaction.tags.forEach(tag => {
                const tagName = tag.name || tag;
                incomeTagStats[tagName] = (incomeTagStats[tagName] || 0) + transaction.amount;
            });
        } else {
            incomeTagStats['Không có tag'] = (incomeTagStats['Không có tag'] || 0) + transaction.amount;
        }
    });

    const incomeTagPieData = Object.entries(incomeTagStats).map(([tag, amount], index) => ({
        id: index,
        value: amount,
        label: tag,
        color: colorPalette[index % colorPalette.length],
    }));

    // Tính toán % chi từ các tag
    const expenseTagStats = {};
    expenseTransactions.forEach(transaction => {
        if (transaction.tags && transaction.tags.length > 0) {
            transaction.tags.forEach(tag => {
                const tagName = tag.name || tag;
                expenseTagStats[tagName] = (expenseTagStats[tagName] || 0) + transaction.amount;
            });
        } else {
            expenseTagStats['Không có tag'] = (expenseTagStats['Không có tag'] || 0) + transaction.amount;
        }
    });

    const expenseTagPieData = Object.entries(expenseTagStats).map(([tag, amount], index) => ({
        id: index,
        value: amount,
        label: tag,
        color: colorPalette[index % colorPalette.length],
    }));

    return (
        <Grid container spacing={3} sx={{ mb: 3 }}>
            {/* Bar Chart Thu-Chi */}
            <Grid size={{ xs: 6, md: 3 }}>
                <Card sx={{ height: "100%", borderRadius: "10px" }}>
                    <CardContent>
                        <Typography variant="h6" gutterBottom>
                            Thu - Chi
                        </Typography>
                        <Typography
                            variant="body2"
                            color={profit >= 0 ? "success.main" : "error.main"}
                            gutterBottom
                            sx={{ fontWeight: 'bold' }}
                        >
                            Lãi: {new Intl.NumberFormat("vi-VN", {
                                style: "currency",
                                currency: "VND",
                            }).format(profit)}
                        </Typography>
                        {/* <BarChart
                            series={[
                                {
                                    data: barChartData.map(item => item.value),
                                    label: 'Số tiền (VND)',
                                    valueFormatter: (value) => new Intl.NumberFormat("vi-VN", {
                                        style: "currency",
                                        currency: "VND",
                                    }).format(value),
                                }
                            ]}
                            xAxis={[{
                                data: barChartData.map(item => item.label),
                                scaleType: 'band'
                            }]}
                            colors={[green[600], red[600]]}
                            height={300}
                            margin={{ left: 80, right: 20, top: 20, bottom: 40 }}
                        /> */}
                        <BarChart
                            series={[
                                {
                                    data: [barChartData[0].value], // cột 1
                                    label: barChartData[0].label,
                                    color: green[600],
                                    valueFormatter: (value) =>
                                        new Intl.NumberFormat("vi-VN", {
                                            style: "currency",
                                            currency: "VND",
                                        }).format(value),
                                },
                                {
                                    data: [barChartData[1].value], // cột 2
                                    label: barChartData[1].label,
                                    color: red[600],
                                    valueFormatter: (value) =>
                                        new Intl.NumberFormat("vi-VN", {
                                            style: "currency",
                                            currency: "VND",
                                        }).format(value),
                                },
                            ]}
                            // xAxis={[
                            //     {
                            //         data: [barChartData[0].label, barChartData[1].label],
                            //         scaleType: "band",
                            //     },
                            // ]}
                            height={300}
                            margin={{ left: 80, right: 20, top: 20, bottom: 40 }}
                        />
                    </CardContent>
                </Card>
            </Grid>

            {/* Pie Chart % Thu-Chi */}
            <Grid size={{ xs: 6, md: 3 }}>
                <Card sx={{ height: "100%", borderRadius: "10px" }}>
                    <CardContent>
                        <Typography variant="h6" gutterBottom>
                            % Thu - Chi
                        </Typography>
                        <PieChart
                            series={[
                                {
                                    data: incomeExpensePieData,
                                    highlightScope: { faded: 'global', highlighted: 'item' },
                                    faded: { innerRadius: 30, additionalRadius: -30, color: 'gray' },
                                    valueFormatter: (value) => new Intl.NumberFormat("vi-VN", {
                                        style: "currency",
                                        currency: "VND",
                                    }).format(value.value),
                                }
                            ]}
                            height={300}
                            slotProps={{
                                legend: {
                                    direction: 'row',
                                    position: { vertical: 'bottom', horizontal: 'middle' },
                                    padding: 0,
                                },
                            }}
                        />
                    </CardContent>
                </Card>
            </Grid>

            {/* Pie Chart % Thu từ các tag */}
            <Grid size={{ xs: 6, md: 3 }}>
                <Card sx={{ height: "100%", borderRadius: "10px" }}>
                    <CardContent>
                        <Typography variant="h6" gutterBottom>
                            % Thu từ các Tag
                        </Typography>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                            Tổng: {new Intl.NumberFormat("vi-VN", {
                                style: "currency",
                                currency: "VND",
                            }).format(totalIncome)}
                        </Typography>
                        {incomeTagPieData.length > 0 ? (
                            <PieChart
                                sx={{ alignItems: "center" }}
                                series={[
                                    {
                                        data: incomeTagPieData,
                                        highlightScope: { faded: 'global', highlighted: 'item' },
                                        faded: { innerRadius: 30, additionalRadius: -30, color: 'gray' },
                                        valueFormatter: (value) => new Intl.NumberFormat("vi-VN", {
                                            style: "currency",
                                            currency: "VND",
                                        }).format(value.value),
                                    }
                                ]}
                                height={300}
                                slotProps={{
                                    legend: {
                                        direction: 'column',
                                        position: { vertical: 'middle', horizontal: 'right' },
                                        padding: 0,
                                    },
                                }}
                            />
                        ) : (
                            <Typography variant="body2" color="text.secondary">
                                Không có dữ liệu thu
                            </Typography>
                        )}
                    </CardContent>
                </Card>
            </Grid>

            {/* Pie Chart % Chi từ các tag */}
            <Grid size={{ xs: 6, md: 3 }}>
                <Card sx={{ height: "100%", borderRadius: "10px" }}>
                    <CardContent>
                        <Typography variant="h6" gutterBottom>
                            % Chi từ các Tag
                        </Typography>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                            Tổng: {new Intl.NumberFormat("vi-VN", {
                                style: "currency",
                                currency: "VND",
                            }).format(totalExpense)}
                        </Typography>
                        {expenseTagPieData.length > 0 ? (
                            <PieChart
                                series={[
                                    {
                                        data: expenseTagPieData,
                                        highlightScope: { faded: 'global', highlighted: 'item' },
                                        faded: { innerRadius: 30, additionalRadius: -30, color: 'gray' },
                                        valueFormatter: (value) => new Intl.NumberFormat("vi-VN", {
                                            style: "currency",
                                            currency: "VND",
                                        }).format(value.value),
                                    }
                                ]}
                                height={300}
                                slotProps={{
                                    legend: {
                                        direction: 'column',
                                        position: { vertical: 'middle', horizontal: 'right' },
                                        padding: 0,
                                    },
                                }}
                            />
                        ) : (
                            <Typography variant="body2" color="text.secondary">
                                Không có dữ liệu chi
                            </Typography>
                        )}
                    </CardContent>
                </Card>
            </Grid>
        </Grid>
    );
}

function formatPrice(number) {
    return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "VND",
    }).format(number);
}

function formatDateYMD(date) {
    return `${date.getFullYear()}-${(date.getMonth() + 1)
        .toString()
        .padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")}`;
}

function AddTransactionModal({ open, handleClose, type, onSubmit }) {
    const [formData, setFormData] = React.useState({
        date: new Date(),
        amount: "",
        description: "",
        category: "cast",
        tags: [],
    });

    const [availableTags, setAvailableTags] = React.useState([]);

    // Fetch available tags when modal opens
    React.useEffect(() => {
        if (open) {
            const fetchTags = async () => {
                try {
                    const response = await axiosInstance.get("/api/admin/transaction-tags");
                    setAvailableTags(response.data);
                } catch (error) {
                    console.error("Lỗi khi tải tags:", error);
                }
            };
            fetchTags();
        }
    }, [open]);

    const handleSubmit = async (e) => {
        e.preventDefault();
        try {
            // Format date to YYYY-MM-DD before submitting
            const formattedDate = formatDateYMD(formData.date);

            await onSubmit({
                ...formData,
                date: formattedDate,
                type: type === "income" ? "income" : "expense",
                tags_data: formData.tags.map(tag => tag.name || tag), // Handle both new and existing tags
            });
            handleClose();
            setFormData({
                date: new Date(),
                amount: "",
                description: "",
                category: "cast",
                tags: [],
            });
        } catch (error) {
            console.error("Lỗi khi thêm giao dịch:", error);
        }
    };

    return (
        <Modal open={open} onClose={handleClose} aria-labelledby="modal-title">
            <Box
                sx={{
                    position: "absolute",
                    top: "50%",
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                    width: 400,
                    maxHeight: "90vh",
                    bgcolor: "background.paper",
                    borderRadius: 2,
                    boxShadow: 24,
                    p: 4,
                    overflow: "auto",
                }}
            >
                <Typography
                    id="modal-title"
                    variant="h6"
                    component="h2"
                    sx={{ mb: 2 }}
                    color={type === "income" ? "success" : "error"}
                >
                    {type === "income" ? "Thêm phiếu thu" : "Thêm phiếu chi"}
                </Typography>

                <form onSubmit={handleSubmit}>
                    <Box
                        sx={{
                            display: "flex",
                            flexDirection: "column",
                            gap: 2,
                        }}
                    >
                        <Calendar
                            date={formData.date}
                            onChange={(newValue) => {
                                setFormData((prev) => ({
                                    ...prev,
                                    date: newValue,
                                }));
                            }}
                        />

                        <FormControl fullWidth>
                            <InputLabel>Phương thức thanh toán</InputLabel>
                            <Select
                                value={formData.category}
                                label="Phương thức thanh toán"
                                onChange={(e) =>
                                    setFormData((prev) => ({
                                        ...prev,
                                        category: e.target.value,
                                    }))
                                }
                                sx={{ borderRadius: "10px" }}
                            >
                                <MenuItem value="cast">Tiền mặt</MenuItem>
                                <MenuItem value="banking">Chuyển khoản</MenuItem>
                                <MenuItem value="visa">Visa</MenuItem>
                            </Select>
                        </FormControl>

                        <CustomTextField
                            label={"Số tiền: " + formatPrice(formData.amount)}
                            type="number"
                            fullWidth
                            value={formData.amount}
                            onChange={(e) =>
                                setFormData((prev) => ({
                                    ...prev,
                                    amount: e.target.value,
                                }))
                            }
                            required
                        />

                        <Autocomplete
                            multiple
                            freeSolo
                            options={availableTags}
                            getOptionLabel={(option) => option.name || option}
                            value={formData.tags}
                            onChange={(_, newValue) => {
                                setFormData((prev) => ({
                                    ...prev,
                                    tags: newValue,
                                }));
                            }}
                            renderTags={(value, getTagProps) =>
                                value.map((option, index) => (
                                    <Chip
                                        variant="outlined"
                                        label={option.name || option}
                                        {...getTagProps({ index })}
                                        key={index}
                                    />
                                ))
                            }
                            renderInput={(params) => (
                                <CustomTextField
                                    {...params}
                                    label="Tags"
                                    placeholder="Nhập tag và nhấn Enter"
                                />
                            )}
                        />

                        <CustomTextField
                            label="Mô tả"
                            multiline
                            rows={4}
                            fullWidth
                            value={formData.description}
                            onChange={(e) =>
                                setFormData((prev) => ({
                                    ...prev,
                                    description: e.target.value,
                                }))
                            }
                            required
                        />

                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "flex-end",
                                gap: 1,
                                mt: 2,
                            }}
                        >
                            <Button onClick={handleClose} sx={{ borderRadius: "10px" }}>
                                Hủy
                            </Button>
                            <Button
                                type="submit"
                                variant="contained"
                                color={type === "income" ? "success" : "error"}
                                sx={{ borderRadius: "10px" }}
                                startIcon={<CheckIcon />}
                            >
                                Lưu
                            </Button>
                        </Box>
                    </Box>
                </form>
            </Box>
        </Modal>
    );
}

function EditTransactionModal({ open, handleClose, transaction, onSubmit }) {
    const [formData, setFormData] = React.useState({
        date: new Date(),
        amount: "",
        description: "",
        type: "income",
        category: "cast",
        tags: [],
    });

    const [availableTags, setAvailableTags] = React.useState([]);

    // Fetch available tags when modal opens
    React.useEffect(() => {
        if (open) {
            const fetchTags = async () => {
                try {
                    const response = await axiosInstance.get("/api/admin/transaction-tags");
                    setAvailableTags(response.data);
                } catch (error) {
                    console.error("Lỗi khi tải tags:", error);
                }
            };
            fetchTags();
        }
    }, [open]);

    React.useEffect(() => {
        if (transaction) {
            setFormData({
                date: new Date(transaction.date),
                amount: transaction.amount.toString(),
                description: transaction.description,
                type: transaction.type,
                category: transaction.category || "cast",
                tags: transaction.tags || [],
            });
        }
    }, [transaction]);

    const handleSubmit = async (e) => {
        e.preventDefault();
        try {
            // Format date to YYYY-MM-DD before submitting
            const formattedDate = formatDateYMD(formData.date);

            await onSubmit({
                ...formData,
                date: formattedDate,
                id: transaction.id,
                tags_data: formData.tags.map(tag => tag.name || tag), // Handle both new and existing tags
            });
            handleClose();
        } catch (error) {
            console.error("Lỗi khi cập nhật giao dịch:", error);
        }
    };

    return (
        <Modal open={open} onClose={handleClose} aria-labelledby="edit-modal-title">
            <Box
                sx={{
                    position: "absolute",
                    top: "50%",
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                    width: 400,
                    bgcolor: "background.paper",
                    borderRadius: 2,
                    boxShadow: 24,
                    p: 4,
                    overflow: "auto",
                    maxHeight: "90vh",
                }}
            >
                <Typography
                    id="edit-modal-title"
                    variant="h6"
                    component="h2"
                    sx={{ mb: 2 }}
                    color={formData.type === "income" ? "success" : "error"}
                >
                    Chỉnh sửa {formData.type === "income" ? "phiếu thu" : "phiếu chi"}
                </Typography>

                <form onSubmit={handleSubmit}>
                    <Box
                        sx={{
                            display: "flex",
                            flexDirection: "column",
                            gap: 2,
                        }}
                    >
                        <FormControl fullWidth>
                            <InputLabel>Loại giao dịch</InputLabel>
                            <Select
                                value={formData.type}
                                label="Loại giao dịch"
                                onChange={(e) =>
                                    setFormData((prev) => ({
                                        ...prev,
                                        type: e.target.value,
                                    }))
                                }
                                sx={{ borderRadius: "10px" }}
                            >
                                <MenuItem value="income">Thu</MenuItem>
                                <MenuItem value="expense">Chi</MenuItem>
                            </Select>
                        </FormControl>

                        <Calendar
                            date={formData.date}
                            onChange={(newValue) => {
                                setFormData((prev) => ({
                                    ...prev,
                                    date: newValue,
                                }));
                            }}
                        />

                        <FormControl fullWidth>
                            <InputLabel>Phương thức thanh toán</InputLabel>
                            <Select
                                value={formData.category}
                                label="Phương thức thanh toán"
                                onChange={(e) =>
                                    setFormData((prev) => ({
                                        ...prev,
                                        category: e.target.value,
                                    }))
                                }
                                sx={{ borderRadius: "10px" }}
                            >
                                <MenuItem value="cast">Tiền mặt</MenuItem>
                                <MenuItem value="banking">Chuyển khoản</MenuItem>
                                <MenuItem value="visa">Visa</MenuItem>
                            </Select>
                        </FormControl>

                        <CustomTextField
                            label={"Số tiền: " + formatPrice(formData.amount)}
                            type="number"
                            fullWidth
                            value={formData.amount}
                            onChange={(e) =>
                                setFormData((prev) => ({
                                    ...prev,
                                    amount: e.target.value,
                                }))
                            }
                            required
                        />

                        <CustomTextField
                            label="Mô tả"
                            multiline
                            rows={4}
                            fullWidth
                            value={formData.description}
                            onChange={(e) =>
                                setFormData((prev) => ({
                                    ...prev,
                                    description: e.target.value,
                                }))
                            }
                            required
                        />

                        <Autocomplete
                            multiple
                            freeSolo
                            options={availableTags}
                            getOptionLabel={(option) => option.name || option}
                            value={formData.tags}
                            onChange={(_, newValue) => {
                                setFormData((prev) => ({
                                    ...prev,
                                    tags: newValue,
                                }));
                            }}
                            renderTags={(value, getTagProps) =>
                                value.map((option, index) => (
                                    <Chip
                                        variant="outlined"
                                        label={option.name || option}
                                        {...getTagProps({ index })}
                                        key={index}
                                    />
                                ))
                            }
                            renderInput={(params) => (
                                <CustomTextField
                                    {...params}
                                    label="Tags"
                                    placeholder="Nhập tag và nhấn Enter"
                                    sx={{ borderRadius: "10px" }}
                                />
                            )}
                        />

                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "flex-end",
                                gap: 1,
                                mt: 2,
                            }}
                        >
                            <Button onClick={handleClose} sx={{ borderRadius: "10px" }}>
                                Hủy
                            </Button>
                            <Button
                                type="submit"
                                variant="contained"
                                color={formData.type === "income" ? "success" : "error"}
                                sx={{ borderRadius: "10px" }}
                                startIcon={<CheckIcon />}
                            >
                                Cập nhật
                            </Button>
                        </Box>
                    </Box>
                </form>
            </Box>
        </Modal>
    );
}

const columns = [
    { id: "id", headerName: "Mã GD", maxWidth: 50 },
    { id: "date", headerName: "Ngày GD", minWidth: 100 },
    { id: "type", headerName: "Loại", width: 90 },
    { id: "category", headerName: "Phương thức", width: 120 },
    { id: "amount", headerName: "Số tiền", align: "right", width: 130 },
    { id: "description", headerName: "Mô tả", width: 200 },
    { id: "tags", headerName: "Tags", width: 150 },
    { id: "name", headerName: "Người tạo", width: 130 },
    { id: "actions", headerName: "Thao tác", width: 100 },
];

function Transaction({ user }) {
    useDocumentTitle("Quản lý Thu - Chi | BeE");
    const today = new Date();

    const [dateFrom, setDateFrom] = React.useState(new Date(today.getFullYear(), today.getMonth(), 1));
    const [dateTo, setDateTo] = React.useState(new Date(today.getFullYear(), today.getMonth() + 1, 0));
    const [typeFilter, setTypeFilter] = React.useState("all");

    const [rows, setRows] = React.useState([]);
    const [loading, setLoading] = React.useState(true);

    React.useEffect(() => {
        const fetchData = async () => {
            try {
                const response = await axiosInstance.get("/api/admin/transaction", {
                    params: {
                        dateFrom: dateFrom,
                        dateTo: dateTo,
                    },
                });
                setRows(response.data);
            } catch (error) {
                console.error("Lỗi: ", JSON.stringify(error));
            } finally {
                setLoading(false);
            }
        };
        fetchData();
    }, [dateFrom, dateTo]);

    const [page, setPage] = React.useState(0);
    const [rowsPerPage, setRowsPerPage] = React.useState(10);

    const handleChangePage = (_, newPage) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(+event.target.value);
        setPage(0);
    };

    //

    const [openModal, setOpenModal] = React.useState(false);
    const [transactionType, setTransactionType] = React.useState("income");

    // Edit modal states
    const [openEditModal, setOpenEditModal] = React.useState(false);
    const [editingTransaction, setEditingTransaction] = React.useState(null);

    const handleAddTransaction = async (formData) => {
        try {
            await axiosInstance.post("/api/admin/transaction", formData);
            // Refresh data after adding
            const updatedData = await axiosInstance.get("/api/admin/transaction", {
                params: { dateFrom, dateTo },
            });
            setRows(updatedData.data);
        } catch (error) {
            console.error("Lỗi khi thêm giao dịch:", JSON.stringify(error));
        }
    };

    const handleEdit = (transaction) => {
        setEditingTransaction(transaction);
        setOpenEditModal(true);
    };

    const handleUpdateTransaction = async (formData) => {
        try {
            await axiosInstance.put(`/api/admin/transaction/${formData.id}`, formData);
            // Refresh data after updating
            const updatedData = await axiosInstance.get("/api/admin/transaction", {
                params: { dateFrom, dateTo },
            });
            setRows(updatedData.data);
            setOpenEditModal(false);
            setEditingTransaction(null);
        } catch (error) {
            console.error("Lỗi khi cập nhật giao dịch:", JSON.stringify(error));
        }
    };

    const handleRemove = async (id) => {
        // Hiển thị confirm dialog
        if (confirm("Bạn có chắc chắn muốn xóa phiếu này không?")) {
            try {
                await axiosInstance.delete(`/api/admin/transaction/${id}`);
                // Refresh data after deleting
                const updatedData = await axiosInstance.get("/api/admin/transaction", {
                    params: { dateFrom, dateTo },
                });
                setRows(updatedData.data);
            } catch (error) {
                console.error("Lỗi khi xóa giao dịch:", JSON.stringify(error));
            }
        }
    };

    return (
        <AdminLayout title="Quản lý Thu - Chi" user={user}>
            <Box sx={{ display: "flex", gap: 2, flexDirection: "column" }}>
                {/* Charts Section */}
                {!loading && (
                    <TransactionCharts
                        transactions={rows.filter((row) => typeFilter === "all" || row.type === typeFilter)}
                    />
                )}

                <Box
                    sx={{
                        display: "flex",
                        gap: 2,
                        justifyContent: "space-between",
                        flexWrap: "wrap",
                    }}
                >
                    <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
                        <CustomDatePicker
                            dateFrom={dateFrom}
                            setDateFrom={setDateFrom}
                            dateTo={dateTo}
                            setDateTo={setDateTo}
                        />
                        <FormControl sx={{ minWidth: 120 }} size="small">
                            <InputLabel>Loại</InputLabel>
                            <Select
                                value={typeFilter}
                                label="Loại"
                                onChange={(e) => setTypeFilter(e.target.value)}
                                sx={{ borderRadius: "10px" }}
                            >
                                <MenuItem value="all">Tất cả</MenuItem>
                                <MenuItem value="income" sx={{ color: green[600] }}>Thu</MenuItem>
                                <MenuItem value="expense" sx={{ color: red[600] }}>Chi</MenuItem>
                            </Select>
                        </FormControl>
                    </Box>
                    <Box sx={{ display: "flex", gap: 2 }}>
                        <Button
                            color="success"
                            startIcon={<AddIcon />}
                            variant="outlined"
                            onClick={() => {
                                setTransactionType("income");
                                setOpenModal(true);
                            }}
                            sx={{
                                borderRadius: "10px",
                                display: { xs: "none", md: "flex" },
                            }}
                        >
                            Thêm phiếu thu
                        </Button>
                        <IconButton
                            color="success"
                            variant="outlined"
                            onClick={() => {
                                setTransactionType("income");
                                setOpenModal(true);
                            }}
                            sx={{
                                borderRadius: "10px",
                                display: { xs: "flex", md: "none" },
                            }}
                        >
                            <AddIcon />
                        </IconButton>
                        <Button
                            color="error"
                            startIcon={<RemoveIcon />}
                            variant="outlined"
                            onClick={() => {
                                setTransactionType("expense");
                                setOpenModal(true);
                            }}
                            sx={{
                                borderRadius: "10px",
                                display: { xs: "none", md: "flex" },
                            }}
                        >
                            Thêm phiếu chi
                        </Button>
                        <IconButton
                            color="error"
                            variant="outlined"
                            onClick={() => {
                                setTransactionType("expense");
                                setOpenModal(true);
                            }}
                            sx={{
                                borderRadius: "10px",
                                display: { xs: "flex", md: "none" },
                            }}
                        >
                            <RemoveIcon />
                        </IconButton>
                        <AddTransactionModal
                            open={openModal}
                            handleClose={() => setOpenModal(false)}
                            type={transactionType}
                            onSubmit={handleAddTransaction}
                        />
                        <EditTransactionModal
                            open={openEditModal}
                            handleClose={() => {
                                setOpenEditModal(false);
                                setEditingTransaction(null);
                            }}
                            transaction={editingTransaction}
                            onSubmit={handleUpdateTransaction}
                        />
                    </Box>
                </Box>
                {loading ? (
                    <Loading />
                ) : (
                    <Paper
                        sx={{
                            width: "100%",
                            maxWidth: { xs: "85vw", md: "100vw" },
                            overflowX: "hidden",
                            borderRadius: "10px",
                        }}
                        elevation={2}
                    >
                        <TableContainer sx={{ maxHeight: 440 }}>
                            <Table stickyHeader aria-label="sticky table">
                                <TableHead>
                                    <TableRow>
                                        {columns.map((column) => (
                                            <TableCell
                                                key={column.id}
                                                align={column.align}
                                                style={{
                                                    minWidth: column.minWidth,
                                                }}
                                            >
                                                {column.headerName}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {rows
                                        .filter((row) => typeFilter === "all" || row.type === typeFilter)
                                        .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                                        .map((row, index) => {
                                            return (
                                                <TableRow hover role="checkbox" tabIndex={-1} key={index}>
                                                    {columns.map((column) => {
                                                        const value = row[column.id];
                                                        return (
                                                            <TableCell key={column.id} align={column.align}>
                                                                {column.id === "type" ? (
                                                                    <Typography
                                                                        color={
                                                                            value === "income"
                                                                                ? "success.main"
                                                                                : "error.main"
                                                                        }
                                                                    >
                                                                        {value === "income" ? "Thu" : "Chi"}
                                                                    </Typography>
                                                                ) : column.id === "category" ? (
                                                                    <Typography>
                                                                        {value === "cast" ? "Tiền mặt" :
                                                                            value === "banking" ? "Chuyển khoản" :
                                                                                value === "visa" ? "Visa" : value}
                                                                    </Typography>
                                                                ) : column.id === "tags" ? (
                                                                    <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
                                                                        {(value || []).map((tag, tagIndex) => (
                                                                            <Chip
                                                                                key={tagIndex}
                                                                                label={tag.name || tag}
                                                                                size="small"
                                                                                variant="outlined"
                                                                            />
                                                                        ))}
                                                                    </Box>
                                                                ) : column.id === "amount" ? (
                                                                    new Intl.NumberFormat("en-US", {
                                                                        style: "currency",
                                                                        currency: "VND",
                                                                    }).format(value)
                                                                ) : column.id === "actions" ? (
                                                                    <Box>
                                                                        <IconButton
                                                                            color="primary"
                                                                            size="small"
                                                                            onClick={() => handleEdit(row)}
                                                                        >
                                                                            <EditIcon />
                                                                        </IconButton>
                                                                        <IconButton
                                                                            color="error"
                                                                            size="small"
                                                                            onClick={() => handleRemove(row.id)}
                                                                        >
                                                                            <DeleteIcon />
                                                                        </IconButton>
                                                                    </Box>
                                                                ) : (
                                                                    value
                                                                )}
                                                            </TableCell>
                                                        );
                                                    })}
                                                </TableRow>
                                            );
                                        })}
                                </TableBody>
                            </Table>
                        </TableContainer>
                        <TablePagination
                            rowsPerPageOptions={[10, 25, 50]}
                            component="div"
                            count={rows.filter((row) => typeFilter === "all" || row.type === typeFilter).length}
                            rowsPerPage={rowsPerPage}
                            page={page}
                            onPageChange={handleChangePage}
                            onRowsPerPageChange={handleChangeRowsPerPage}
                            labelRowsPerPage={"Hiển thị:"}
                        />
                    </Paper>
                )}
            </Box>
        </AdminLayout >
    );
}

export default Transaction;
