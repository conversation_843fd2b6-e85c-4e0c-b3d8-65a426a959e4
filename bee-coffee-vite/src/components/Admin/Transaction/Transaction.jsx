import React from "react";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TablePagination from "@mui/material/TablePagination";
import TableRow from "@mui/material/TableRow";
import AdminLayout from "../Common/AdminLayout2";
import { Box, Button, IconButton, Modal, Typography, FormControl, InputLabel, Select, MenuItem, Autocomplete, Chip } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
import CustomDatePicker from "../../Common/CustomDatePicker";
import CustomTextField from "../../Common/CustomTextField";
import axiosInstance from "../../../services/axiosInstance";
import Loading from "../../Common/Loading";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import "react-date-range/dist/styles.css"; // main css file
import "react-date-range/dist/theme/default.css"; // theme css file
import { Calendar } from "react-date-range";
import CheckIcon from "@mui/icons-material/Check";
import { useDocumentTitle } from "../../../hooks/useDocumentTitle";
import { green, red } from "@mui/material/colors";

function AddTransactionModal({ open, handleClose, type, onSubmit }) {
    const [formData, setFormData] = React.useState({
        date: new Date(),
        amount: "",
        description: "",
        category: "cast",
        tags: [],
    });

    const [availableTags, setAvailableTags] = React.useState([]);

    // Fetch available tags when modal opens
    React.useEffect(() => {
        if (open) {
            const fetchTags = async () => {
                try {
                    const response = await axiosInstance.get("/api/admin/transaction-tags");
                    setAvailableTags(response.data);
                } catch (error) {
                    console.error("Lỗi khi tải tags:", error);
                }
            };
            fetchTags();
        }
    }, [open]);

    const handleSubmit = async (e) => {
        e.preventDefault();
        try {
            // Format date to YYYY-MM-DD before submitting
            const formattedDate = formData.date.toISOString().split("T")[0];

            await onSubmit({
                ...formData,
                date: formattedDate,
                type: type === "income" ? "income" : "expense",
                tags_data: formData.tags.map(tag => tag.name || tag), // Handle both new and existing tags
            });
            handleClose();
            setFormData({
                date: new Date(),
                amount: "",
                description: "",
                category: "cast",
                tags: [],
            });
        } catch (error) {
            console.error("Lỗi khi thêm giao dịch:", error);
        }
    };

    return (
        <Modal open={open} onClose={handleClose} aria-labelledby="modal-title">
            <Box
                sx={{
                    position: "absolute",
                    top: "50%",
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                    width: 400,
                    bgcolor: "background.paper",
                    borderRadius: 2,
                    boxShadow: 24,
                    p: 4,
                }}
            >
                <Typography
                    id="modal-title"
                    variant="h6"
                    component="h2"
                    sx={{ mb: 2 }}
                    color={type === "income" ? "success" : "error"}
                >
                    {type === "income" ? "Thêm phiếu thu" : "Thêm phiếu chi"}
                </Typography>

                <form onSubmit={handleSubmit}>
                    <Box
                        sx={{
                            display: "flex",
                            flexDirection: "column",
                            gap: 2,
                        }}
                    >
                        <Calendar
                            date={formData.date}
                            onChange={(newValue) => {
                                setFormData((prev) => ({
                                    ...prev,
                                    date: newValue,
                                }));
                            }}
                        />

                        <FormControl fullWidth>
                            <InputLabel>Phương thức thanh toán</InputLabel>
                            <Select
                                value={formData.category}
                                label="Phương thức thanh toán"
                                onChange={(e) =>
                                    setFormData((prev) => ({
                                        ...prev,
                                        category: e.target.value,
                                    }))
                                }
                            >
                                <MenuItem value="cast">Tiền mặt</MenuItem>
                                <MenuItem value="banking">Chuyển khoản</MenuItem>
                                <MenuItem value="visa">Visa</MenuItem>
                            </Select>
                        </FormControl>

                        <CustomTextField
                            label="Số tiền"
                            type="number"
                            fullWidth
                            value={formData.amount}
                            onChange={(e) =>
                                setFormData((prev) => ({
                                    ...prev,
                                    amount: e.target.value,
                                }))
                            }
                            required
                        />

                        <Autocomplete
                            multiple
                            freeSolo
                            options={availableTags}
                            getOptionLabel={(option) => option.name || option}
                            value={formData.tags}
                            onChange={(_, newValue) => {
                                setFormData((prev) => ({
                                    ...prev,
                                    tags: newValue,
                                }));
                            }}
                            renderTags={(value, getTagProps) =>
                                value.map((option, index) => (
                                    <Chip
                                        variant="outlined"
                                        label={option.name || option}
                                        {...getTagProps({ index })}
                                        key={index}
                                    />
                                ))
                            }
                            renderInput={(params) => (
                                <CustomTextField
                                    {...params}
                                    label="Tags"
                                    placeholder="Nhập tag và nhấn Enter"
                                />
                            )}
                        />

                        <CustomTextField
                            label="Mô tả"
                            multiline
                            rows={4}
                            fullWidth
                            value={formData.description}
                            onChange={(e) =>
                                setFormData((prev) => ({
                                    ...prev,
                                    description: e.target.value,
                                }))
                            }
                            required
                        />

                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "flex-end",
                                gap: 1,
                                mt: 2,
                            }}
                        >
                            <Button onClick={handleClose} sx={{ borderRadius: "10px" }}>
                                Hủy
                            </Button>
                            <Button
                                type="submit"
                                variant="contained"
                                color={type === "income" ? "success" : "error"}
                                sx={{ borderRadius: "10px" }}
                                startIcon={<CheckIcon />}
                            >
                                Lưu
                            </Button>
                        </Box>
                    </Box>
                </form>
            </Box>
        </Modal>
    );
}

function EditTransactionModal({ open, handleClose, transaction, onSubmit }) {
    const [formData, setFormData] = React.useState({
        date: new Date(),
        amount: "",
        description: "",
        type: "income",
        category: "cast",
        tags: [],
    });

    const [availableTags, setAvailableTags] = React.useState([]);

    // Fetch available tags when modal opens
    React.useEffect(() => {
        if (open) {
            const fetchTags = async () => {
                try {
                    const response = await axiosInstance.get("/api/admin/transaction-tags");
                    setAvailableTags(response.data);
                } catch (error) {
                    console.error("Lỗi khi tải tags:", error);
                }
            };
            fetchTags();
        }
    }, [open]);

    React.useEffect(() => {
        if (transaction) {
            setFormData({
                date: new Date(transaction.date),
                amount: transaction.amount.toString(),
                description: transaction.description,
                type: transaction.type,
                category: transaction.category || "cast",
                tags: transaction.tags || [],
            });
        }
    }, [transaction]);

    const handleSubmit = async (e) => {
        e.preventDefault();
        try {
            // Format date to YYYY-MM-DD before submitting
            const formattedDate = formData.date.toISOString().split("T")[0];

            await onSubmit({
                ...formData,
                date: formattedDate,
                id: transaction.id,
                tags_data: formData.tags.map(tag => tag.name || tag), // Handle both new and existing tags
            });
            handleClose();
        } catch (error) {
            console.error("Lỗi khi cập nhật giao dịch:", error);
        }
    };

    return (
        <Modal open={open} onClose={handleClose} aria-labelledby="edit-modal-title">
            <Box
                sx={{
                    position: "absolute",
                    top: "50%",
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                    width: 400,
                    bgcolor: "background.paper",
                    borderRadius: 2,
                    boxShadow: 24,
                    p: 4,
                }}
            >
                <Typography
                    id="edit-modal-title"
                    variant="h6"
                    component="h2"
                    sx={{ mb: 2 }}
                    color={formData.type === "income" ? "success" : "error"}
                >
                    Chỉnh sửa {formData.type === "income" ? "phiếu thu" : "phiếu chi"}
                </Typography>

                <form onSubmit={handleSubmit}>
                    <Box
                        sx={{
                            display: "flex",
                            flexDirection: "column",
                            gap: 2,
                        }}
                    >
                        <FormControl fullWidth>
                            <InputLabel>Loại giao dịch</InputLabel>
                            <Select
                                value={formData.type}
                                label="Loại giao dịch"
                                onChange={(e) =>
                                    setFormData((prev) => ({
                                        ...prev,
                                        type: e.target.value,
                                    }))
                                }
                            >
                                <MenuItem value="income">Thu</MenuItem>
                                <MenuItem value="expense">Chi</MenuItem>
                            </Select>
                        </FormControl>

                        <FormControl fullWidth>
                            <InputLabel>Phương thức thanh toán</InputLabel>
                            <Select
                                value={formData.category}
                                label="Phương thức thanh toán"
                                onChange={(e) =>
                                    setFormData((prev) => ({
                                        ...prev,
                                        category: e.target.value,
                                    }))
                                }
                            >
                                <MenuItem value="cast">Tiền mặt</MenuItem>
                                <MenuItem value="banking">Chuyển khoản</MenuItem>
                                <MenuItem value="visa">Visa</MenuItem>
                            </Select>
                        </FormControl>

                        <Calendar
                            date={formData.date}
                            onChange={(newValue) => {
                                setFormData((prev) => ({
                                    ...prev,
                                    date: newValue,
                                }));
                            }}
                        />

                        <CustomTextField
                            label="Số tiền"
                            type="number"
                            fullWidth
                            value={formData.amount}
                            onChange={(e) =>
                                setFormData((prev) => ({
                                    ...prev,
                                    amount: e.target.value,
                                }))
                            }
                            required
                        />

                        <CustomTextField
                            label="Mô tả"
                            multiline
                            rows={4}
                            fullWidth
                            value={formData.description}
                            onChange={(e) =>
                                setFormData((prev) => ({
                                    ...prev,
                                    description: e.target.value,
                                }))
                            }
                            required
                        />

                        <Autocomplete
                            multiple
                            freeSolo
                            options={availableTags}
                            getOptionLabel={(option) => option.name || option}
                            value={formData.tags}
                            onChange={(_, newValue) => {
                                setFormData((prev) => ({
                                    ...prev,
                                    tags: newValue,
                                }));
                            }}
                            renderTags={(value, getTagProps) =>
                                value.map((option, index) => (
                                    <Chip
                                        variant="outlined"
                                        label={option.name || option}
                                        {...getTagProps({ index })}
                                        key={index}
                                    />
                                ))
                            }
                            renderInput={(params) => (
                                <CustomTextField
                                    {...params}
                                    label="Tags"
                                    placeholder="Nhập tag và nhấn Enter"
                                />
                            )}
                        />

                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "flex-end",
                                gap: 1,
                                mt: 2,
                            }}
                        >
                            <Button onClick={handleClose} sx={{ borderRadius: "10px" }}>
                                Hủy
                            </Button>
                            <Button
                                type="submit"
                                variant="contained"
                                color={formData.type === "income" ? "success" : "error"}
                                sx={{ borderRadius: "10px" }}
                                startIcon={<CheckIcon />}
                            >
                                Cập nhật
                            </Button>
                        </Box>
                    </Box>
                </form>
            </Box>
        </Modal>
    );
}

const columns = [
    { id: "id", headerName: "Mã GD", maxWidth: 50 },
    { id: "date", headerName: "Ngày GD", minWidth: 100 },
    { id: "type", headerName: "Loại", width: 90 },
    { id: "category", headerName: "Phương thức", width: 120 },
    { id: "amount", headerName: "Số tiền", align: "right", width: 130 },
    { id: "description", headerName: "Mô tả", width: 200 },
    { id: "tags", headerName: "Tags", width: 150 },
    { id: "name", headerName: "Người tạo", width: 130 },
    { id: "actions", headerName: "Thao tác", width: 100 },
];

function Transaction({ user }) {
    useDocumentTitle("Quản lý Thu - Chi | BeE");
    const today = new Date();

    const [dateFrom, setDateFrom] = React.useState(new Date(today.getFullYear(), today.getMonth(), 1));
    const [dateTo, setDateTo] = React.useState(new Date(today.getFullYear(), today.getMonth() + 1, 0));
    const [typeFilter, setTypeFilter] = React.useState("all");

    const [rows, setRows] = React.useState([]);
    const [loading, setLoading] = React.useState(true);

    React.useEffect(() => {
        const fetchData = async () => {
            try {
                const response = await axiosInstance.get("/api/admin/transaction", {
                    params: {
                        dateFrom: dateFrom,
                        dateTo: dateTo,
                    },
                });
                setRows(response.data);
            } catch (error) {
                console.error("Lỗi: ", JSON.stringify(error));
            } finally {
                setLoading(false);
            }
        };
        fetchData();
    }, [dateFrom, dateTo]);

    const [page, setPage] = React.useState(0);
    const [rowsPerPage, setRowsPerPage] = React.useState(10);

    const handleChangePage = (_, newPage) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(+event.target.value);
        setPage(0);
    };

    //

    const [openModal, setOpenModal] = React.useState(false);
    const [transactionType, setTransactionType] = React.useState("income");

    // Edit modal states
    const [openEditModal, setOpenEditModal] = React.useState(false);
    const [editingTransaction, setEditingTransaction] = React.useState(null);

    const handleAddTransaction = async (formData) => {
        try {
            await axiosInstance.post("/api/admin/transaction", formData);
            // Refresh data after adding
            const updatedData = await axiosInstance.get("/api/admin/transaction", {
                params: { dateFrom, dateTo },
            });
            setRows(updatedData.data);
        } catch (error) {
            console.error("Lỗi khi thêm giao dịch:", JSON.stringify(error));
        }
    };

    const handleEdit = (transaction) => {
        setEditingTransaction(transaction);
        setOpenEditModal(true);
    };

    const handleUpdateTransaction = async (formData) => {
        try {
            await axiosInstance.put(`/api/admin/transaction/${formData.id}`, formData);
            // Refresh data after updating
            const updatedData = await axiosInstance.get("/api/admin/transaction", {
                params: { dateFrom, dateTo },
            });
            setRows(updatedData.data);
            setOpenEditModal(false);
            setEditingTransaction(null);
        } catch (error) {
            console.error("Lỗi khi cập nhật giao dịch:", JSON.stringify(error));
        }
    };

    const handleRemove = async (id) => {
        // Hiển thị confirm dialog
        if (confirm("Bạn có chắc chắn muốn xóa phiếu này không?")) {
            try {
                await axiosInstance.delete(`/api/admin/transaction/${id}`);
                // Refresh data after deleting
                const updatedData = await axiosInstance.get("/api/admin/transaction", {
                    params: { dateFrom, dateTo },
                });
                setRows(updatedData.data);
            } catch (error) {
                console.error("Lỗi khi xóa giao dịch:", JSON.stringify(error));
            }
        }
    };

    return (
        <AdminLayout title="Quản lý Thu - Chi" user={user}>
            <Box sx={{ display: "flex", gap: 2, flexDirection: "column" }}>
                <Box
                    sx={{
                        display: "flex",
                        gap: 2,
                        justifyContent: "space-between",
                        flexWrap: "wrap",
                    }}
                >
                    <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
                        <CustomDatePicker
                            dateFrom={dateFrom}
                            setDateFrom={setDateFrom}
                            dateTo={dateTo}
                            setDateTo={setDateTo}
                        />
                        <FormControl sx={{ minWidth: 120 }} size="small">
                            <InputLabel>Loại</InputLabel>
                            <Select
                                value={typeFilter}
                                label="Loại"
                                onChange={(e) => setTypeFilter(e.target.value)}
                                sx={{ borderRadius: "10px" }}
                            >
                                <MenuItem value="all">Tất cả</MenuItem>
                                <MenuItem value="income" sx={{ color: green[600] }}>Thu</MenuItem>
                                <MenuItem value="expense" sx={{ color: red[600] }}>Chi</MenuItem>
                            </Select>
                        </FormControl>
                    </Box>
                    <Box sx={{ display: "flex", gap: 2 }}>
                        <Button
                            color="success"
                            startIcon={<AddIcon />}
                            variant="outlined"
                            onClick={() => {
                                setTransactionType("income");
                                setOpenModal(true);
                            }}
                            sx={{
                                borderRadius: "10px",
                                display: { xs: "none", md: "flex" },
                            }}
                        >
                            Thêm phiếu thu
                        </Button>
                        <IconButton
                            color="success"
                            variant="outlined"
                            onClick={() => {
                                setTransactionType("income");
                                setOpenModal(true);
                            }}
                            sx={{
                                borderRadius: "10px",
                                display: { xs: "flex", md: "none" },
                            }}
                        >
                            <AddIcon />
                        </IconButton>
                        <Button
                            color="error"
                            startIcon={<RemoveIcon />}
                            variant="outlined"
                            onClick={() => {
                                setTransactionType("expense");
                                setOpenModal(true);
                            }}
                            sx={{
                                borderRadius: "10px",
                                display: { xs: "none", md: "flex" },
                            }}
                        >
                            Thêm phiếu chi
                        </Button>
                        <IconButton
                            color="error"
                            variant="outlined"
                            onClick={() => {
                                setTransactionType("expense");
                                setOpenModal(true);
                            }}
                            sx={{
                                borderRadius: "10px",
                                display: { xs: "flex", md: "none" },
                            }}
                        >
                            <RemoveIcon />
                        </IconButton>
                        <AddTransactionModal
                            open={openModal}
                            handleClose={() => setOpenModal(false)}
                            type={transactionType}
                            onSubmit={handleAddTransaction}
                        />
                        <EditTransactionModal
                            open={openEditModal}
                            handleClose={() => {
                                setOpenEditModal(false);
                                setEditingTransaction(null);
                            }}
                            transaction={editingTransaction}
                            onSubmit={handleUpdateTransaction}
                        />
                    </Box>
                </Box>
                {loading ? (
                    <Loading />
                ) : (
                    <Paper
                        sx={{
                            width: "100%",
                            maxWidth: { xs: "85vw", md: "100vw" },
                            overflowX: "hidden",
                            borderRadius: "10px",
                        }}
                        elevation={2}
                    >
                        <TableContainer sx={{ maxHeight: 440 }}>
                            <Table stickyHeader aria-label="sticky table">
                                <TableHead>
                                    <TableRow>
                                        {columns.map((column) => (
                                            <TableCell
                                                key={column.id}
                                                align={column.align}
                                                style={{
                                                    minWidth: column.minWidth,
                                                }}
                                            >
                                                {column.headerName}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {rows
                                        .filter((row) => typeFilter === "all" || row.type === typeFilter)
                                        .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                                        .map((row, index) => {
                                            return (
                                                <TableRow hover role="checkbox" tabIndex={-1} key={index}>
                                                    {columns.map((column) => {
                                                        const value = row[column.id];
                                                        return (
                                                            <TableCell key={column.id} align={column.align}>
                                                                {column.id === "type" ? (
                                                                    <Typography
                                                                        color={
                                                                            value === "income"
                                                                                ? "success.main"
                                                                                : "error.main"
                                                                        }
                                                                    >
                                                                        {value === "income" ? "Thu" : "Chi"}
                                                                    </Typography>
                                                                ) : column.id === "category" ? (
                                                                    <Typography>
                                                                        {value === "cast" ? "Tiền mặt" :
                                                                            value === "banking" ? "Chuyển khoản" :
                                                                                value === "visa" ? "Visa" : value}
                                                                    </Typography>
                                                                ) : column.id === "tags" ? (
                                                                    <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
                                                                        {(value || []).map((tag, tagIndex) => (
                                                                            <Chip
                                                                                key={tagIndex}
                                                                                label={tag.name || tag}
                                                                                size="small"
                                                                                variant="outlined"
                                                                            />
                                                                        ))}
                                                                    </Box>
                                                                ) : column.id === "amount" ? (
                                                                    new Intl.NumberFormat("en-US", {
                                                                        style: "currency",
                                                                        currency: "VND",
                                                                    }).format(value)
                                                                ) : column.id === "actions" ? (
                                                                    <Box>
                                                                        <IconButton
                                                                            color="primary"
                                                                            size="small"
                                                                            onClick={() => handleEdit(row)}
                                                                        >
                                                                            <EditIcon />
                                                                        </IconButton>
                                                                        <IconButton
                                                                            color="error"
                                                                            size="small"
                                                                            onClick={() => handleRemove(row.id)}
                                                                        >
                                                                            <DeleteIcon />
                                                                        </IconButton>
                                                                    </Box>
                                                                ) : (
                                                                    value
                                                                )}
                                                            </TableCell>
                                                        );
                                                    })}
                                                </TableRow>
                                            );
                                        })}
                                </TableBody>
                            </Table>
                        </TableContainer>
                        <TablePagination
                            rowsPerPageOptions={[10, 25, 50]}
                            component="div"
                            count={rows.filter((row) => typeFilter === "all" || row.type === typeFilter).length}
                            rowsPerPage={rowsPerPage}
                            page={page}
                            onPageChange={handleChangePage}
                            onRowsPerPageChange={handleChangeRowsPerPage}
                            labelRowsPerPage={"Hiển thị:"}
                        />
                    </Paper>
                )}
            </Box>
        </AdminLayout >
    );
}

export default Transaction;
